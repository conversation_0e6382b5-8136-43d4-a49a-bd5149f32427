/**
 * Quick test for public booking API
 */

const axios = require('axios');

async function quickTest() {
  try {
    console.log('🧪 Quick test for public booking...');
    
    const response = await axios.post('http://localhost:3000/api/public/bookings', {
      formSlug: 'cng-ty-tnhh-nng-vng',
      customerName: 'Quick Test',
      phoneNumber: '0123456789',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-25',
      preferredTime: '10:00',
      specialRequests: 'Quick test'
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 10000
    });

    console.log('✅ SUCCESS!');
    console.log('Status:', response.status);
    console.log('Booking ID:', response.data.data.id);
    console.log('Booking Code:', response.data.data.bookingCode);
    
  } catch (error) {
    console.log('❌ ERROR:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.response?.data?.error?.message || error.message);
  }
}

quickTest();
