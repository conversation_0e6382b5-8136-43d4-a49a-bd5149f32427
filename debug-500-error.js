/**
 * Debug script to identify the exact cause of the 500 error
 */

const axios = require('axios');

async function debug500Error() {
  console.log('🔍 Debugging 500 Internal Server Error...\n');

  try {
    // Test the exact same request that's failing
    const bookingData = {
      formSlug: 'cng-ty-tnhh-nng-vng',
      customerName: 'Test Customer',
      phoneNumber: '0123456789',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-25',
      preferredTime: '10:00',
      specialRequests: 'Debug test for 500 error'
    };

    console.log('📝 Sending request to /api/public/bookings');
    console.log('Request data:', JSON.stringify(bookingData, null, 2));

    const response = await axios.post('http://localhost:3000/api/public/bookings', bookingData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    console.log('✅ Unexpected success!');
    console.log('Response:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ Error caught:');
    console.log('Status:', error.response?.status);
    console.log('Status Text:', error.response?.statusText);
    
    if (error.response?.data) {
      console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Connection refused - is the server running on port 3000?');
      return;
    }
    
    if (error.code === 'ENOTFOUND') {
      console.log('🌐 DNS resolution failed - check the URL');
      return;
    }
    
    // Try to get more details about the 500 error
    if (error.response?.status === 500) {
      console.log('\n🔍 500 Internal Server Error Details:');
      console.log('This suggests a server-side issue. Common causes:');
      console.log('1. Database connection issues');
      console.log('2. Model association errors');
      console.log('3. Missing database columns');
      console.log('4. Transaction handling issues');
      console.log('5. Import/require errors');
      
      // Test if the form exists first
      console.log('\n🧪 Testing if form exists...');
      try {
        const formResponse = await axios.get(`http://localhost:3000/api/public/forms/${bookingData.formSlug}`);
        console.log('✅ Form exists:', formResponse.data.data.name);
      } catch (formError) {
        console.log('❌ Form test failed:', formError.response?.data?.error?.message || formError.message);
      }
    }
  }
}

// Test database connection separately
async function testDatabaseConnection() {
  console.log('\n🗄️ Testing database connection...');
  
  try {
    // Try a simple database query
    const testResponse = await axios.get('http://localhost:3000/api/test');
    console.log('✅ Basic API connection works');
  } catch (error) {
    console.log('❌ Basic API connection failed:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  await debug500Error();
  await testDatabaseConnection();
}

if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { debug500Error, testDatabaseConnection };
