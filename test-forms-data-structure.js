/**
 * Test forms data structure and frontend processing
 */

// Simulate backend response structure
const mockBackendResponse = {
  success: true,
  data: {
    forms: [
      {
        id: 1,
        name: 'Hair Cut Booking Form',
        slug: 'hair-cut-booking',
        status: 'active',
        user_id: 1,
        service: {
          id: 1,
          name: 'Hair Cut',
          price: 50000,
          duration: 60
        },
        branch: {
          id: 1,
          name: 'Main Branch',
          address: '123 Main St',
          phone: '0123456789'
        },
        created_at: '2025-01-01T00:00:00Z',
        publicUrl: 'http://localhost:3001/book/hair-cut-booking',
        embedCode: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800"></iframe>',
        qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=http://localhost:3001/book/hair-cut-booking',
        socialUrls: {
          facebook: 'https://www.facebook.com/sharer/sharer.php?u=http://localhost:3001/book/hair-cut-booking',
          twitter: 'https://twitter.com/intent/tweet?url=http://localhost:3001/book/hair-cut-booking'
        },
        embedCodes: {
          standard: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800"></iframe>',
          compact: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="400" height="600"></iframe>',
          fullHeight: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800"></iframe>',
          mobile: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="100%" height="600"></iframe>',
          styled: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800" style="border:none;box-shadow:0 4px 6px rgba(0,0,0,0.1);border-radius:8px;"></iframe>',
          javascript: '<script>/* Dynamic loading code */</script>',
          button: '<button onclick="window.open(\'http://localhost:3001/book/hair-cut-booking\')">Book Now</button>'
        },
        bookingsThisMonth: 5,
        lastBooking: '2025-01-15T10:30:00Z'
      },
      {
        id: 2,
        name: 'Massage Booking Form',
        slug: 'massage-booking',
        status: 'active',
        user_id: 1,
        service: {
          id: 2,
          name: 'Relaxing Massage',
          price: 80000,
          duration: 90
        },
        branch: {
          id: 1,
          name: 'Main Branch',
          address: '123 Main St',
          phone: '0123456789'
        },
        created_at: '2025-01-02T00:00:00Z',
        publicUrl: 'http://localhost:3001/book/massage-booking',
        embedCode: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800"></iframe>',
        qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=http://localhost:3001/book/massage-booking',
        socialUrls: {
          facebook: 'https://www.facebook.com/sharer/sharer.php?u=http://localhost:3001/book/massage-booking',
          twitter: 'https://twitter.com/intent/tweet?url=http://localhost:3001/book/massage-booking'
        },
        embedCodes: {
          standard: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800"></iframe>',
          compact: '<iframe src="http://localhost:3001/book/massage-booking" width="400" height="600"></iframe>',
          fullHeight: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800"></iframe>',
          mobile: '<iframe src="http://localhost:3001/book/massage-booking" width="100%" height="600"></iframe>',
          styled: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800" style="border:none;box-shadow:0 4px 6px rgba(0,0,0,0.1);border-radius:8px;"></iframe>',
          javascript: '<script>/* Dynamic loading code */</script>',
          button: '<button onclick="window.open(\'http://localhost:3001/book/massage-booking\')">Book Now</button>'
        },
        bookingsThisMonth: 3,
        lastBooking: '2025-01-14T14:00:00Z'
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 2,
      pages: 1
    }
  },
  message: 'Forms retrieved successfully'
};

function testFormsDataStructure() {
  console.log('🧪 Testing Forms Data Structure and Frontend Processing\n');

  // Simulate what the backend returns
  console.log('1. Backend Response Structure:');
  console.log('   Response keys:', Object.keys(mockBackendResponse));
  console.log('   Response.data keys:', Object.keys(mockBackendResponse.data));
  console.log('   Forms count:', mockBackendResponse.data.forms.length);
  console.log('   Has pagination:', !!mockBackendResponse.data.pagination);

  // Simulate the OLD frontend processing (BROKEN)
  console.log('\n2. OLD Frontend Processing (BROKEN):');
  const oldResult = { success: true, data: mockBackendResponse.data };
  console.log('   Frontend receives data:', Object.keys(oldResult.data));
  console.log('   Frontend tries to access: result.data.forms');
  console.log('   Result:', oldResult.data.forms ? '✅ Works' : '❌ Broken');

  // Simulate the NEW frontend processing (FIXED)
  console.log('\n3. NEW Frontend Processing (FIXED):');
  const responseData = mockBackendResponse.data || {};
  const newResult = { 
    success: true, 
    data: {
      forms: responseData.forms || [],
      pagination: responseData.pagination || {}
    }
  };
  console.log('   Frontend receives data:', Object.keys(newResult.data));
  console.log('   Frontend accesses: result.data.forms');
  console.log('   Forms array:', Array.isArray(newResult.data.forms) ? '✅ Valid array' : '❌ Not array');
  console.log('   Forms count:', newResult.data.forms.length);

  // Simulate frontend component processing
  console.log('\n4. Frontend Component Processing:');
  const forms = newResult.data.forms || [];
  console.log('   Component state: forms =', forms.length, 'items');
  
  if (forms.length > 0) {
    console.log('   ✅ Forms will be displayed in UI');
    console.log('   Forms list:');
    forms.forEach((form, index) => {
      console.log(`     ${index + 1}. ${form.name} (${form.status})`);
      console.log(`        - Service: ${form.service?.name || 'N/A'}`);
      console.log(`        - Branch: ${form.branch?.name || 'N/A'}`);
      console.log(`        - Public URL: ${form.publicUrl ? '✅ Present' : '❌ Missing'}`);
      console.log(`        - Embed Code: ${form.embedCode ? '✅ Present' : '❌ Missing'}`);
      console.log(`        - Bookings: ${form.bookingsThisMonth || 0} this month`);
    });
  } else {
    console.log('   ❌ No forms - empty state will be shown');
  }

  // Test the specific frontend component logic
  console.log('\n5. Frontend Component Logic Test:');
  console.log('   forms.length > 0:', forms.length > 0);
  console.log('   Will show forms list:', forms.length > 0 ? 'YES' : 'NO (empty state)');
  
  if (forms.length > 0) {
    console.log('   First form rendering test:');
    const firstForm = forms[0];
    console.log('     - Name:', firstForm.name);
    console.log('     - Service name:', firstForm.service?.name || 'No service');
    console.log('     - Branch name:', firstForm.branch?.name || 'No branch');
    console.log('     - Created date:', firstForm.createdAt || firstForm.created_at || 'Unknown');
    console.log('     - Status badge:', firstForm.status);
    console.log('     - Public URL for preview:', firstForm.publicUrl || firstForm.standaloneLink || '#');
  }

  console.log('\n🎉 Data Structure Analysis Complete!');
  console.log('\n📋 Summary:');
  console.log('   ✅ Backend returns correct structure');
  console.log('   ✅ Frontend service fix handles the structure properly');
  console.log('   ✅ Frontend component will receive forms array');
  console.log('   ✅ Forms will be displayed in the UI');
  console.log('\n💡 The fix should resolve the "no forms displayed" issue!');
}

// Run the test
if (require.main === module) {
  testFormsDataStructure();
}

module.exports = { testFormsDataStructure, mockBackendResponse };
