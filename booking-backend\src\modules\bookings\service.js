/**
 * Booking Service
 * Business logic for booking management
 */

const { Op } = require('sequelize');
const { sequelize } = require('../../database/connection');
const Booking = require('./model');
const Customer = require('../customers/model');
const Service = require('../services/model');
const Branch = require('../branches/model');
const Employee = require('../employees/model');
const User = require('../users/model');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class BookingService {
  /**
   * Create a new booking
   */
  static async createBooking(bookingData, userId) {
    try {
      // Generate booking code
      const bookingCode = Booking.generateBookingCode();

      // Create booking
      const booking = await Booking.create({
        bookingCode,
        customerId: bookingData.customerId,
        serviceId: bookingData.serviceId,
        branchId: bookingData.branchId,
        employeeId: bookingData.employeeId,
        bookingDate: bookingData.bookingDate,
        startTime: bookingData.startTime,
        endTime: bookingData.endTime,
        duration: bookingData.duration || 60,
        totalAmount: bookingData.totalAmount || 0,
        discountAmount: bookingData.discountAmount || 0,
        finalAmount: bookingData.finalAmount || bookingData.totalAmount || 0,
        notes: bookingData.notes,
        customerNotes: bookingData.customerNotes,
        internalNotes: bookingData.internalNotes
      });

      logger.info('Booking created successfully', {
        bookingId: booking.id,
        bookingCode: booking.bookingCode,
        customerId: bookingData.customerId,
        serviceId: bookingData.serviceId,
        createdBy: userId
      });

      return await this.getBookingById(booking.id);
    } catch (error) {
      logger.error('Create booking failed', {
        error: error.message,
        bookingData,
        userId
      });
      throw error;
    }
  }

  /**
   * Get all bookings with pagination and filtering
   */
  static async getAllBookings(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        customerId,
        serviceId,
        branchId,
        employeeId,
        bookingDate,
        startDate,
        endDate,
        paymentStatus,
        search
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Apply filters
      if (status) whereClause.status = status;
      if (customerId) whereClause.customerId = customerId;
      if (serviceId) whereClause.serviceId = serviceId;
      if (branchId) whereClause.branchId = branchId;
      if (employeeId) whereClause.employeeId = employeeId;
      if (bookingDate) whereClause.bookingDate = bookingDate;
      if (paymentStatus) whereClause.paymentStatus = paymentStatus;

      if (startDate && endDate) {
        whereClause.bookingDate = {
          [Op.between]: [startDate, endDate]
        };
      }

      if (search) {
        whereClause[Op.or] = [
          { bookingCode: { [Op.iLike]: `%${search}%` } },
          { notes: { [Op.iLike]: `%${search}%` } }
        ];
      }

      const { count, rows } = await Booking.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Customer,
            as: 'customer',
            include: [{ model: User, as: 'user', attributes: ['id', 'name', 'email', 'phone'] }]
          },
          { model: Service, as: 'service', attributes: ['id', 'name', 'category', 'duration', 'price'] },
          { model: Branch, as: 'branch', attributes: ['id', 'name', 'address', 'phone'] },
          {
            model: Employee,
            as: 'employee',
            include: [{ model: User, as: 'user', attributes: ['id', 'name', 'email', 'phone'] }]
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      return {
        bookings: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          nextPage: page < totalPages ? parseInt(page) + 1 : null,
          prevPage: page > 1 ? parseInt(page) - 1 : null
        }
      };
    } catch (error) {
      logger.error('Get all bookings failed', { error: error.message, options });
      throw error;
    }
  }

  /**
   * Get booking by ID
   */
  static async getBookingById(id) {
    try {
      const booking = await Booking.findByPk(id, {
        include: [
          {
            model: Customer,
            as: 'customer',
            include: [{ model: User, as: 'user', attributes: ['id', 'name', 'email', 'phone'] }]
          },
          { model: Service, as: 'service' },
          { model: Branch, as: 'branch' },
          {
            model: Employee,
            as: 'employee',
            include: [{ model: User, as: 'user', attributes: ['id', 'name', 'email', 'phone'] }]
          }
        ]
      });

      if (!booking) {
        throw new AppError('Booking not found', 404, 'BOOKING_NOT_FOUND');
      }

      return booking;
    } catch (error) {
      logger.error('Get booking by ID failed', { error: error.message, bookingId: id });
      throw error;
    }
  }

  /**
   * Get booking by booking code
   */
  static async getBookingByCode(code) {
    try {
      const booking = await Booking.findOne({
        where: { bookingCode: code },
        include: [
          {
            model: Customer,
            as: 'customer',
            include: [{ model: User, as: 'user', attributes: ['id', 'name', 'email', 'phone'] }]
          },
          { model: Service, as: 'service' },
          { model: Branch, as: 'branch' },
          {
            model: Employee,
            as: 'employee',
            include: [{ model: User, as: 'user', attributes: ['id', 'name', 'email', 'phone'] }]
          }
        ]
      });

      if (!booking) {
        throw new AppError('Booking not found', 404, 'BOOKING_NOT_FOUND');
      }

      return booking;
    } catch (error) {
      logger.error('Get booking by code failed', { error: error.message, bookingCode: code });
      throw error;
    }
  }

  /**
   * Update booking
   */
  static async updateBooking(id, updateData, userId) {
    try {
      const booking = await this.getBookingById(id);
      await booking.update(updateData);

      logger.info('Booking updated successfully', { bookingId: id, updateData, updatedBy: userId });
      return await this.getBookingById(id);
    } catch (error) {
      logger.error('Update booking failed', { error: error.message, bookingId: id, updateData, userId });
      throw error;
    }
  }

  /**
   * Cancel booking
   */
  static async cancelBooking(id, cancellationData, userId) {
    try {
      const booking = await this.getBookingById(id);

      await booking.update({
        status: 'cancelled',
        cancellationReason: cancellationData.reason,
        cancelledBy: userId,
        cancelledAt: new Date()
      });

      logger.info('Booking cancelled successfully', { bookingId: id, reason: cancellationData.reason, cancelledBy: userId });
      return await this.getBookingById(id);
    } catch (error) {
      logger.error('Cancel booking failed', { error: error.message, bookingId: id, cancellationData, userId });
      throw error;
    }
  }

  /**
   * Confirm booking
   */
  static async confirmBooking(id, userId) {
    try {
      const booking = await this.getBookingById(id);

      await booking.update({
        status: 'confirmed',
        confirmedBy: userId,
        confirmedAt: new Date()
      });

      logger.info('Booking confirmed successfully', { bookingId: id, confirmedBy: userId });
      return await this.getBookingById(id);
    } catch (error) {
      logger.error('Confirm booking failed', { error: error.message, bookingId: id, userId });
      throw error;
    }
  }

  /**
   * Complete booking
   */
  static async completeBooking(id, completionData, userId) {
    try {
      const booking = await this.getBookingById(id);

      await booking.update({
        status: 'completed',
        completedBy: userId,
        completedAt: new Date(),
        completionNotes: completionData.notes
      });

      logger.info('Booking completed successfully', { bookingId: id, completedBy: userId });
      return await this.getBookingById(id);
    } catch (error) {
      logger.error('Complete booking failed', { error: error.message, bookingId: id, completionData, userId });
      throw error;
    }
  }

  /**
   * Get booking statistics
   */
  static async getBookingStats(options = {}) {
    try {
      const { startDate, endDate, branchId, employeeId } = options;
      const whereClause = {};

      if (startDate && endDate) {
        whereClause.bookingDate = { [Op.between]: [startDate, endDate] };
      }
      if (branchId) whereClause.branchId = branchId;
      if (employeeId) whereClause.employeeId = employeeId;

      const totalBookings = await Booking.count({ where: whereClause });
      
      const statusStats = await Booking.findAll({
        where: whereClause,
        attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['status'],
        raw: true
      });

      const paymentStats = await Booking.findAll({
        where: whereClause,
        attributes: ['paymentStatus', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['paymentStatus'],
        raw: true
      });

      return { totalBookings, statusStats, paymentStats };
    } catch (error) {
      logger.error('Get booking stats failed', { error: error.message, options });
      throw error;
    }
  }

  /**
   * Get available time slots
   */
  static async getAvailableTimeSlots(branchId, serviceId, employeeId, date) {
    try {
      // Simple implementation - return basic time slots
      const timeSlots = ['08:00', '09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00'];
      return timeSlots;
    } catch (error) {
      logger.error('Get available time slots failed', { error: error.message, branchId, serviceId, employeeId, date });
      throw error;
    }
  }

  /**
   * Delete booking (soft delete)
   */
  static async deleteBooking(id, userId) {
    try {
      const booking = await this.getBookingById(id);
      await booking.destroy();

      logger.info('Booking deleted successfully', { bookingId: id, deletedBy: userId });
      return { message: 'Booking deleted successfully' };
    } catch (error) {
      logger.error('Delete booking failed', { error: error.message, bookingId: id, userId });
      throw error;
    }
  }

  /**
   * Create booking from public form (no authentication required)
   */
  static async createPublicBooking(data) {
    const transaction = await sequelize.transaction();
    
    try {
      const { formSlug, customerName, phoneNumber, emailAddress, preferredDate, preferredTime, specialRequests } = data;

      // First, get form by slug to validate and get service/branch info
      const FormsService = require('../forms/service');
      const form = await FormsService.getFormBySlug(formSlug);

      if (!form) {
        throw new AppError('Form not found or inactive', 404);
      }

      // Find or create customer using proper User -> Customer relationship
      const User = require('../users/model');
      const Customer = require('../customers/model');

      let user = await User.findOne({
        where: { email: emailAddress }
      });

      if (!user) {
        // Create new user with customer role
        // Generate a random password for public customers (they can reset it later)
        const bcrypt = require('bcrypt');
        const randomPassword = Math.random().toString(36).slice(-8);
        const hashedPassword = await bcrypt.hash(randomPassword, 10);

        user = await User.create({
          name: customerName,
          email: emailAddress,
          phone: phoneNumber,
          password: hashedPassword, // Hashed random password
          role: 'customer',
          is_active: true
        }, { transaction });

        logger.info('New user created from public booking', {
          userId: user.id,
          email: emailAddress,
          formSlug
        });
      }

      // Find or create customer record
      let customer = await Customer.findOne({
        where: { userId: user.id }
      });

      if (!customer) {
        // Generate unique customer code
        const customerCode = `CUS${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

        customer = await Customer.create({
          userId: user.id,
          customerCode: customerCode,
          membershipLevel: 'bronze',
          membershipPoints: 0,
          totalSpent: 0,
          status: 'active'
        }, { transaction });

        logger.info('New customer record created from public booking', {
          customerId: customer.id,
          userId: user.id,
          customerCode: customerCode,
          formSlug
        });
      }

      // Use user.id as customer identifier

      // Generate booking code
      const bookingCode = Booking.generateBookingCode();

      // Calculate booking times (add service duration to preferred time)
      const [hours, minutes] = preferredTime.split(':').map(Number);
      const startTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      
      // Add service duration to get end time
      const serviceDuration = form.service.duration || 60;
      const endTimeMinutes = hours * 60 + minutes + serviceDuration;
      const endHours = Math.floor(endTimeMinutes / 60);
      const endMins = endTimeMinutes % 60;
      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;

      // Create booking
      const booking = await Booking.create({
        bookingCode,
        customerId: customer.id, // Use customer.id (not user.id) for proper foreign key relationship
        serviceId: form.service_id,
        branchId: form.branch_id,
        // formId: form.id, // TODO: Add this after database migration is applied
        employeeId: null, // Will be assigned later by staff
        bookingDate: preferredDate,
        startTime,
        endTime,
        duration: serviceDuration,
        totalAmount: parseFloat(form.service.price) || 0,
        discountAmount: 0,
        finalAmount: parseFloat(form.service.price) || 0,
        status: 'pending',
        notes: specialRequests || null,
        customerNotes: specialRequests || null,
        internalNotes: `Booking created from public form: ${form.name} (${formSlug})`
      }, { transaction });

      // Commit transaction before querying related data
      await transaction.commit();

      logger.info('Public booking created successfully', {
        bookingId: booking.id,
        bookingCode: booking.bookingCode,
        customerId: customer.id,
        userId: user.id,
        serviceId: form.service_id,
        branchId: form.branch_id,
        formSlug
      });

      // Return booking with related data (outside transaction)
      const createdBooking = await Booking.findByPk(booking.id, {
        include: [
          {
            model: Customer,
            as: 'customer',
            attributes: ['id', 'customerCode', 'membershipLevel'],
            include: [{
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'phone', 'email']
            }]
          },
          {
            model: Service,
            as: 'service',
            attributes: ['id', 'name', 'price', 'duration', 'description']
          },
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'address', 'phone', 'city']
          }
        ]
      });

      return createdBooking;
    } catch (error) {
      // Only rollback if transaction is still active
      if (!transaction.finished) {
        await transaction.rollback();
      }
      logger.error('Create public booking failed', { error: error.message, data });
      throw error;
    }
  }
}

module.exports = BookingService;
