/**
 * Debug script for forms listing issue
 */

const axios = require('axios');

async function debugFormsListing() {
  console.log('🔍 Debugging Forms Listing Issue...\n');

  try {
    // Step 1: Login to get authentication token
    console.log('1. Logging in...');
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    console.log(`✅ Login successful`);
    console.log(`   User ID: ${user.id}`);
    console.log(`   User Role: ${user.role}`);
    console.log(`   User Email: ${user.email}\n`);

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Test forms API endpoint
    console.log('2. Testing forms API endpoint...');
    try {
      const formsResponse = await axios.get('http://localhost:3000/api/forms', { headers });
      console.log(`✅ Forms API successful`);
      console.log(`   Status: ${formsResponse.status}`);
      console.log(`   Response structure:`, Object.keys(formsResponse.data));
      
      if (formsResponse.data.data) {
        console.log(`   Forms data structure:`, Object.keys(formsResponse.data.data));
        if (formsResponse.data.data.forms) {
          console.log(`   Total forms: ${formsResponse.data.data.forms.length}`);
          formsResponse.data.data.forms.forEach((form, index) => {
            console.log(`   - Form ${index + 1}: ${form.name} (ID: ${form.id}, User ID: ${form.user_id || form.userId})`);
          });
        } else {
          console.log(`   ⚠️ No 'forms' property in response data`);
          console.log(`   Response data:`, JSON.stringify(formsResponse.data.data, null, 2));
        }
      } else {
        console.log(`   ⚠️ No 'data' property in response`);
        console.log(`   Full response:`, JSON.stringify(formsResponse.data, null, 2));
      }
    } catch (error) {
      console.log(`❌ Forms API error: ${error.response?.data?.error?.message || error.message}`);
      console.log(`   Status: ${error.response?.status}`);
      if (error.response?.data) {
        console.log(`   Error details:`, JSON.stringify(error.response.data, null, 2));
      }
    }

    // Step 3: Check if forms exist in database by creating one
    console.log('\n3. Testing form creation...');
    try {
      // First get branches and services
      const branchesResponse = await axios.get('http://localhost:3000/api/branches', { headers });
      const servicesResponse = await axios.get('http://localhost:3000/api/services', { headers });
      
      console.log(`   Available branches: ${branchesResponse.data.data?.length || 0}`);
      console.log(`   Available services: ${servicesResponse.data.data?.length || 0}`);
      
      if (branchesResponse.data.data?.length > 0 && servicesResponse.data.data?.length > 0) {
        const branch = branchesResponse.data.data[0];
        const service = servicesResponse.data.data[0];
        
        const formData = {
          name: `Debug Test Form ${Date.now()}`,
          serviceId: service.id,
          branchId: branch.id,
          status: 'active'
        };
        
        console.log(`   Creating form with data:`, formData);
        const createResponse = await axios.post('http://localhost:3000/api/forms', formData, { headers });
        console.log(`✅ Form created successfully`);
        console.log(`   Form ID: ${createResponse.data.data.id}`);
        console.log(`   Form Slug: ${createResponse.data.data.slug}`);
        
        // Now test forms listing again
        console.log('\n4. Re-testing forms listing after creation...');
        const newFormsResponse = await axios.get('http://localhost:3000/api/forms', { headers });
        console.log(`   Total forms now: ${newFormsResponse.data.data.forms?.length || 0}`);
        
      } else {
        console.log(`   ⚠️ Cannot create form - missing branches or services`);
      }
      
    } catch (error) {
      console.log(`❌ Form creation error: ${error.response?.data?.error?.message || error.message}`);
    }

    // Step 4: Check database directly (if possible)
    console.log('\n5. Summary of findings...');
    console.log('   - Authentication: Working');
    console.log('   - API endpoint: Accessible');
    console.log('   - User-based filtering: Check if forms belong to current user');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
  }
}

// Run the debug
if (require.main === module) {
  debugFormsListing().catch(console.error);
}

module.exports = { debugFormsListing };
