import api from '../lib/api';

/**
 * Forms Service
 * Handles all API calls related to forms management
 */
class FormsService {  /**
   * Create a new form
   */
  static async createForm(formData) {
    try {
      console.log('=== FormsService.createForm - Frontend Debug ===');
      console.log('Input formData:', JSON.stringify(formData, null, 2));
      
      const requestData = {
        name: formData.name,
        serviceId: parseInt(formData.serviceId),
        branchId: parseInt(formData.branchId),
        status: formData.status || 'active',
        fieldsConfig: formData.fields || {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        },
        brandingConfig: {
          primaryColor: '#3b82f6',
          logo: null,
          customMessage: null
        }
      };
      
      console.log('Request data to send:', JSON.stringify(requestData, null, 2));
      
      const response = await api.post('/forms', requestData);
      
      console.log('=== API Response Analysis ===');
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);
      console.log('Response data structure:', Object.keys(response.data));
      console.log('Response.data:', JSON.stringify(response.data, null, 2));
      
      if (response.data && response.data.data) {
        const formData = response.data.data;
        console.log('Form data keys:', Object.keys(formData));
        console.log('Form data analysis:');
        console.log('  - id:', formData.id);
        console.log('  - name:', formData.name);
        console.log('  - slug:', formData.slug);
        console.log('  - publicUrl:', formData.publicUrl || '❌ MISSING');
        console.log('  - embedCode:', formData.embedCode ? '✅ Present' : '❌ MISSING');
        
        if (formData.publicUrl) {
          console.log('  - publicUrl type:', typeof formData.publicUrl);
          console.log('  - publicUrl length:', formData.publicUrl.length);
        }
        
        if (formData.embedCode) {
          console.log('  - embedCode type:', typeof formData.embedCode);
          console.log('  - embedCode length:', formData.embedCode.length);
          console.log('  - embedCode preview:', formData.embedCode.substring(0, 100) + '...');
        }
      } else {
        console.log('❌ No form data in response.data.data');
      }
      
      console.log('=== End Frontend Debug ===');
      
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('=== FormsService Error ===');
      console.error('Error message:', error.message);
      console.error('Error status:', error.response?.status);
      console.error('Error response data:', error.response?.data);
      console.error('Full error:', error);
      console.error('=== End Error Debug ===');

      // Handle different error types with specific messages
      if (error.response?.status === 401) {
        return {
          success: false,
          error: 'Please log in to create forms. You need to be authenticated to access this feature.',
          errorType: 'AUTHENTICATION_REQUIRED',
          redirectTo: '/login'
        };
      } else if (error.response?.status === 404) {
        const errorData = error.response?.data;
        if (errorData?.error?.code === 'NOT_FOUND') {
          return {
            success: false,
            error: 'Service or branch not found. The 404 error occurs because you need to create prerequisites first.',
            errorType: 'MISSING_PREREQUISITES',
            suggestions: [
              'Create a branch first in the Branches section',
              'Create a service for your branch in the Services section',
              'Ensure the service is linked to your branch',
              'The service must belong to a branch where you are the manager'
            ],
            technicalNote: 'This is not a missing API endpoint - it\'s business logic validation that requires valid service-branch relationships.'
          };
        }
        return {
          success: false,
          error: error.response?.data?.error?.message || 'Resource not found',
          errorType: 'NOT_FOUND'
        };
      } else if (error.response?.status === 400) {
        return {
          success: false,
          error: error.response?.data?.error?.message || 'Invalid form data. Please check your inputs.',
          errorType: 'VALIDATION_ERROR',
          validationErrors: error.response?.data?.error?.details || []
        };
      }

      return {
        success: false,
        error: error.response?.data?.error?.message || error.response?.data?.message || 'Failed to create form. Please try again.',
        errorType: 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * Get all forms for authenticated user
   */
  static async getUserForms(options = {}) {
    try {
      const params = new URLSearchParams();

      if (options.page) params.append('page', options.page);
      if (options.limit) params.append('limit', options.limit);
      if (options.status) params.append('status', options.status);
      if (options.search) params.append('search', options.search);

      console.log('🔍 FormsService.getUserForms - Debug');
      console.log('   Request params:', params.toString());

      const response = await api.get(`/forms?${params.toString()}`);

      console.log('   Response structure:', Object.keys(response.data));
      console.log('   Response.data.data structure:', Object.keys(response.data.data || {}));

      // Backend returns: { success: true, data: { forms: [...], pagination: {...} } }
      // Frontend expects: { success: true, data: { forms: [...] } }
      const responseData = response.data.data || {};

      console.log('   Forms count:', responseData.forms?.length || 0);

      return {
        success: true,
        data: {
          forms: responseData.forms || [],
          pagination: responseData.pagination || {}
        }
      };
    } catch (error) {
      console.error('Get user forms error:', error);
      console.error('   Status:', error.response?.status);
      console.error('   Error data:', error.response?.data);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.response?.data?.message || 'Failed to fetch forms'
      };
    }
  }

  /**
   * Get form by ID
   */
  static async getFormById(id) {
    try {
      const response = await api.get(`/forms/${id}`);
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Get form by ID error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch form' 
      };
    }
  }

  /**
   * Get form by slug (public access)
   */
  static async getFormBySlug(slug) {
    try {
      const response = await api.get(`/public/forms/${slug}`);
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Get form by slug error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Form not found' 
      };
    }
  }

  /**
   * Update form
   */
  static async updateForm(id, formData) {
    try {
      const response = await api.put(`/forms/${id}`, {
        name: formData.name,
        serviceId: formData.serviceId ? parseInt(formData.serviceId) : undefined,
        branchId: formData.branchId ? parseInt(formData.branchId) : undefined,
        status: formData.status,
        fieldsConfig: formData.fields,
        brandingConfig: formData.branding
      });
      
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Update form error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to update form' 
      };
    }
  }

  /**
   * Delete form
   */
  static async deleteForm(id) {
    try {
      const response = await api.delete(`/forms/${id}`);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Delete form error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to delete form' 
      };
    }
  }

  /**
   * Get form statistics
   */
  static async getFormStats() {
    try {
      const response = await api.get('/forms/stats');
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Get form stats error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch form statistics' 
      };
    }
  }

  /**
   * Submit booking from public form (no auth required)
   */
  static async submitPublicBooking(bookingData) {
    try {
      // Don't use the authenticated api instance, use direct axios for public endpoints
      const axios = require('axios');
      const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'}/public/bookings`, bookingData);
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Submit public booking error:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to submit booking' 
      };
    }
  }
}

export default FormsService;
