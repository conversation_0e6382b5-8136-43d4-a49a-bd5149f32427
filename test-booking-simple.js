/**
 * Simple test for public booking submission
 */

const axios = require('axios');

async function testBooking() {
  try {
    console.log('Testing public booking submission...');
    
    const bookingData = {
      formSlug: 'cng-ty-tnhh-nng-vng',
      customerName: 'Test Customer',
      phoneNumber: '0123456789',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-25',
      preferredTime: '10:00',
      specialRequests: 'Test booking'
    };
    
    const response = await axios.post('http://localhost:3000/api/public/bookings', bookingData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Success!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ Error:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.response?.data?.error?.message || error.message);
  }
}

testBooking();
