/**
 * Debug script to test Services API and understand the filtering issue
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testServicesAPI() {
  console.log('🔍 Testing Services API Debug...\n');

  try {
    // 1. Login with the user from frontend
    console.log('1. Logging in with test user...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    console.log(`✅ Login successful`);
    console.log(`   User ID: ${user.id}`);
    console.log(`   User Role: ${user.role}`);
    console.log(`   User Email: ${user.email}\n`);

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Test branches API
    console.log('2. Testing branches API...');
    try {
      const branchesResponse = await axios.get(`${API_BASE}/branches`, { headers });
      console.log(`✅ Branches API successful`);
      console.log(`   Total branches: ${branchesResponse.data.data.data.length}`);
      if (branchesResponse.data.data.data.length > 0) {
        branchesResponse.data.data.data.forEach(branch => {
          console.log(`   - Branch ID: ${branch.id}, Name: ${branch.name}, Manager ID: ${branch.managerId || branch.manager_id}`);
        });
      }
    } catch (error) {
      console.log(`❌ Branches API error: ${error.response?.data?.error?.message || error.message}`);
    }
    console.log('');

    // 3. Test services API with different parameters
    console.log('3. Testing services API...');
    
    // Test 3a: Basic services call (same as frontend)
    try {
      const servicesResponse = await axios.get(`${API_BASE}/services`, { 
        headers,
        params: { isActive: true, limit: 100 }
      });
      console.log(`✅ Services API successful`);
      console.log(`   Total services: ${servicesResponse.data.data.data.length}`);
      if (servicesResponse.data.data.data.length > 0) {
        servicesResponse.data.data.data.forEach(service => {
          console.log(`   - Service ID: ${service.id}, Name: ${service.name}, Branch ID: ${service.branchId || service.branch_id}`);
        });
      } else {
        console.log(`   ⚠️ No services returned - this explains the frontend issue!`);
      }
    } catch (error) {
      console.log(`❌ Services API error: ${error.response?.data?.error?.message || error.message}`);
      console.log(`   Status: ${error.response?.status}`);
    }
    console.log('');

    // Test 3b: Try without filters
    console.log('3b. Testing services API without filters...');
    try {
      const servicesResponse = await axios.get(`${API_BASE}/services`, { headers });
      console.log(`✅ Services API (no filters) successful`);
      console.log(`   Total services: ${servicesResponse.data.data.data.length}`);
    } catch (error) {
      console.log(`❌ Services API (no filters) error: ${error.response?.data?.error?.message || error.message}`);
    }
    console.log('');

    // 4. Check if user has any data at all
    console.log('4. Checking user data ownership...');
    
    // Check all branches (admin view)
    try {
      const allBranchesResponse = await axios.get(`${API_BASE}/branches?limit=200`, { headers });
      const userBranches = allBranchesResponse.data.data.data.filter(b => 
        b.managerId === user.id || b.manager_id === user.id
      );
      console.log(`   User manages ${userBranches.length} branches`);
      
      if (userBranches.length > 0) {
        userBranches.forEach(branch => {
          console.log(`   - Managed Branch: ${branch.name} (ID: ${branch.id})`);
        });
      }
    } catch (error) {
      console.log(`   Could not check branch ownership: ${error.message}`);
    }

    // 5. Create test data if needed
    console.log('\n5. Creating test data for user...');
    
    // Create a branch for the user
    try {
      const branchData = {
        name: `Test Branch ${Date.now()}`,
        address: '123 Test Street',
        phone: `09${Math.floor(Math.random() * 100000000)}`,
        city: 'Ho Chi Minh City',
        district: 'District 1',
        ward: 'Ward 1',
        description: 'Test branch for form creation'
      };
      
      const branchResponse = await axios.post(`${API_BASE}/branches`, branchData, { headers });
      console.log(`✅ Branch created: ${branchResponse.data.data.name} (ID: ${branchResponse.data.data.id})`);
      
      // Create a service linked to this branch
      const serviceData = {
        name: `Test Service ${Date.now()}`,
        description: 'Test service for form creation',
        duration: 60,
        price: 100000,
        category: 'hair_care',
        branchId: branchResponse.data.data.id
      };
      
      const serviceResponse = await axios.post(`${API_BASE}/services`, serviceData, { headers });
      console.log(`✅ Service created: ${serviceResponse.data.data.name} (ID: ${serviceResponse.data.data.id})`);
      
      // Test services API again
      console.log('\n6. Re-testing services API after creating data...');
      const newServicesResponse = await axios.get(`${API_BASE}/services`, { 
        headers,
        params: { isActive: true, limit: 100 }
      });
      console.log(`✅ Services API now returns: ${newServicesResponse.data.data.data.length} services`);
      
    } catch (error) {
      console.log(`❌ Error creating test data: ${error.response?.data?.error?.message || error.message}`);
      if (error.response?.data?.error?.code === 'PHONE_EXISTS') {
        console.log(`   This is expected if test data already exists`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
  }
}

// Run the test
testServicesAPI().catch(console.error);
