-- Migration to add form_id column to bookings table
-- This allows tracking which form was used to create each booking

USE spa_booking_development;

-- Add form_id column to bookings table
ALTER TABLE bookings 
ADD COLUMN form_id INT NULL 
AFTER branch_id,
ADD CONSTRAINT fk_bookings_form_id 
FOREIGN KEY (form_id) REFERENCES forms(id) 
ON UPDATE CASCADE 
ON DELETE SET NULL;

-- Add index for better query performance
CREATE INDEX idx_bookings_form_id ON bookings(form_id);

-- Update existing bookings to have NULL form_id (they were created without forms)
-- This is safe since the column is nullable

SELECT 'Migration completed: Added form_id column to bookings table' as status;
