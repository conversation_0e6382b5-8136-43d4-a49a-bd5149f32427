/**
 * Simple test for forms API without authentication
 */

const axios = require('axios');

async function testFormsAPI() {
  console.log('🧪 Testing Forms API...\n');

  try {
    // Test if server is running
    console.log('1. Testing server health...');
    try {
      const healthResponse = await axios.get('http://localhost:3000/health');
      console.log('✅ Server is running');
      console.log('   Response:', healthResponse.data);
    } catch (error) {
      console.log('❌ Server health check failed:', error.message);
    }

    // Test forms endpoint without auth (should fail with 401)
    console.log('\n2. Testing forms endpoint without auth...');
    try {
      const formsResponse = await axios.get('http://localhost:3000/api/forms');
      console.log('✅ Forms endpoint accessible (unexpected)');
      console.log('   Response:', formsResponse.data);
    } catch (error) {
      console.log(`❌ Forms endpoint error (expected): ${error.response?.status} - ${error.response?.data?.error?.message || error.message}`);
    }

    // Test if there's a mock server running
    console.log('\n3. Checking for mock data...');
    try {
      const mockResponse = await axios.get('http://localhost:3000/api/forms');
      if (mockResponse.data && mockResponse.data.success) {
        console.log('✅ Mock server detected');
        console.log('   Mock forms count:', mockResponse.data.data?.length || 0);
        console.log('   Response structure:', Object.keys(mockResponse.data));
        
        // Check if it's the server-minimal.js mock
        if (Array.isArray(mockResponse.data.data)) {
          console.log('   This appears to be the server-minimal.js mock server');
          console.log('   Forms data:', mockResponse.data.data.map(f => ({ id: f.id, name: f.name, slug: f.slug })));
        }
      }
    } catch (error) {
      console.log('   No mock server detected');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testFormsAPI().catch(console.error);
}

module.exports = { testFormsAPI };
