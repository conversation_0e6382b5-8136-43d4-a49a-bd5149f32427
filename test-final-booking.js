/**
 * Final test for public booking submission after fixes
 */

const axios = require('axios');

async function testFinalBooking() {
  console.log('🧪 Final test for public booking submission...\n');

  try {
    const bookingData = {
      formSlug: 'cng-ty-tnhh-nng-vng',
      customerName: 'Final Test Customer',
      phoneNumber: '0999888777',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-25',
      preferredTime: '15:00',
      specialRequests: 'Final test after fixing 500 error'
    };

    console.log('📝 Sending booking request...');
    console.log('Data:', JSON.stringify(bookingData, null, 2));

    const response = await axios.post('http://localhost:3000/api/public/bookings', bookingData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('\n✅ SUCCESS! Booking created successfully!');
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));

    // Verify the booking data
    const booking = response.data.data;
    console.log('\n📋 Booking Details:');
    console.log(`- Booking ID: ${booking.id}`);
    console.log(`- Booking Code: ${booking.bookingCode}`);
    console.log(`- Customer: ${booking.customer?.user?.name || 'N/A'}`);
    console.log(`- Service: ${booking.service?.name || 'N/A'}`);
    console.log(`- Branch: ${booking.branch?.name || 'N/A'}`);
    console.log(`- Date: ${booking.bookingDate}`);
    console.log(`- Time: ${booking.startTime} - ${booking.endTime}`);
    console.log(`- Status: ${booking.status}`);
    console.log(`- Amount: ${booking.finalAmount}`);

    return true;

  } catch (error) {
    console.log('\n❌ ERROR:');
    console.log('Status:', error.response?.status);
    console.log('Status Text:', error.response?.statusText);
    
    if (error.response?.data) {
      console.log('Error Details:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error Message:', error.message);
    }

    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Server is not running. Please start the backend server first.');
    }

    return false;
  }
}

// Test multiple scenarios
async function runComprehensiveTest() {
  console.log('🔍 Running comprehensive booking test...\n');

  // Test 1: Basic booking
  console.log('=== Test 1: Basic Booking ===');
  const test1 = await testFinalBooking();

  // Test 2: Different customer
  console.log('\n=== Test 2: Different Customer ===');
  try {
    const bookingData2 = {
      formSlug: 'cng-ty-tnhh-nng-vng',
      customerName: 'Another Customer',
      phoneNumber: '0888777666',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-26',
      preferredTime: '10:00',
      specialRequests: 'Second test booking'
    };

    const response2 = await axios.post('http://localhost:3000/api/public/bookings', bookingData2, {
      headers: { 'Content-Type': 'application/json' }
    });

    console.log('✅ Second booking successful!');
    console.log('Booking Code:', response2.data.data.bookingCode);

  } catch (error) {
    console.log('❌ Second booking failed:', error.response?.data?.error?.message || error.message);
  }

  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`- Basic booking: ${test1 ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`- Multiple bookings: ${test1 ? '✅ PASSED' : '❌ FAILED'}`);

  if (test1) {
    console.log('\n🎉 All tests passed! Public booking submission is working correctly.');
    console.log('💡 The 500 Internal Server Error has been resolved.');
  } else {
    console.log('\n⚠️ Tests failed. Please check the error details above.');
  }
}

if (require.main === module) {
  runComprehensiveTest().catch(console.error);
}

module.exports = { testFinalBooking, runComprehensiveTest };
