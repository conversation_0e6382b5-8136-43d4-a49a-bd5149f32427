/**
 * Branches Controller
 * HTTP request handlers for branch management
 */

const BranchesService = require('./service');
const { 
  successResponse, 
  createdResponse, 
  paginatedResponse,
  errorResponse,
  validationErrorResponse 
} = require('../../utils/response');
const { validationResult } = require('express-validator');
const { getPaginationParams, buildSortOrder } = require('../../utils/pagination');
const logger = require('../../utils/logger');

class BranchesController {
  /**
   * Get all branches with pagination and filters
   * GET /api/branches
   */
  async getBranches(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { page, limit } = getPaginationParams(req.query);
      const { city, district, isActive, search, hasLocation, sort } = req.query;

      const filters = { city, district, isActive, search, hasLocation };
      const pagination = { 
        page, 
        limit,
        order: buildSortOrder(sort, ['name', 'city', 'createdAt'], [['name', 'ASC']])
      };

      // Pass user info to service for filtering
      const result = await BranchesService.getBranches(filters, pagination, req.user);

      logger.info('Branches retrieved successfully', {
        userId: req.user.id,
        userRole: req.user.role,
        filters,
        pagination: { page, limit },
        resultCount: result.data.length
      });

      return paginatedResponse(res, result.data, result.pagination, 'Branches retrieved successfully');

    } catch (error) {
      logger.error('Get branches controller error:', error);
      next(error);
    }
  }

  /**
   * Get branch by ID
   * GET /api/branches/:id
   */
  async getBranchById(req, res, next) {
    try {
      const { id } = req.params;
      const branch = await BranchesService.getBranchById(parseInt(id), req.user);

      logger.info('Branch retrieved successfully', {
        userId: req.user.id,
        branchId: id
      });

      return successResponse(res, branch, 'Branch retrieved successfully');

    } catch (error) {
      logger.error('Get branch by ID controller error:', error);
      next(error);
    }
  }

  /**
   * Create new branch
   * POST /api/branches
   */
  async createBranch(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const branchData = req.body;
      
      // If user is customer, automatically set them as manager
      if (req.user.role === 'customer' && !branchData.managerId) {
        branchData.managerId = req.user.id;
      }

      const branch = await BranchesService.createBranch(branchData, req.user);

      logger.info('Branch created successfully', {
        userId: req.user.id,
        userRole: req.user.role,
        branchId: branch.id,
        branchName: branch.name,
        city: branch.city
      });

      return createdResponse(res, branch, 'Branch created successfully');

    } catch (error) {
      logger.error('Create branch controller error:', error);
      next(error);
    }
  }

  /**
   * Update branch
   * PUT /api/branches/:id
   */
  async updateBranch(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const updateData = req.body;
      
      const branch = await BranchesService.updateBranch(parseInt(id), updateData, req.user);

      logger.info('Branch updated successfully', {
        userId: req.user.id,
        userRole: req.user.role,
        branchId: id,
        updatedFields: Object.keys(updateData)
      });

      return successResponse(res, branch, 'Branch updated successfully');

    } catch (error) {
      logger.error('Update branch controller error:', error);
      next(error);
    }
  }

  /**
   * Delete branch
   * DELETE /api/branches/:id
   */
  async deleteBranch(req, res, next) {
    try {
      const { id } = req.params;
      const result = await BranchesService.deleteBranch(parseInt(id), req.user);

      logger.info('Branch deleted successfully', {
        userId: req.user.id,
        userRole: req.user.role,
        branchId: id
      });

      return successResponse(res, result, 'Branch deleted successfully');

    } catch (error) {
      logger.error('Delete branch controller error:', error);
      next(error);
    }
  }

  /**
   * Toggle branch status
   * PATCH /api/branches/:id/status
   */
  async toggleBranchStatus(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { isActive } = req.body;

      const branch = await BranchesService.toggleBranchStatus(parseInt(id), isActive, req.user);

      logger.info('Branch status changed successfully', {
        userId: req.user.id,
        userRole: req.user.role,
        branchId: id,
        newStatus: isActive ? 'active' : 'inactive'
      });

      return successResponse(res, branch, `Branch ${isActive ? 'activated' : 'deactivated'} successfully`);

    } catch (error) {
      logger.error('Toggle branch status controller error:', error);
      next(error);
    }
  }

  /**
   * Get branches by city
   * GET /api/branches/city/:city
   */
  async getBranchesByCity(req, res, next) {
    try {
      const { city } = req.params;
      const { limit = 50 } = req.query;

      const branches = await BranchesService.getBranchesByCity(city, {
        limit: parseInt(limit)
      }, req.user);

      logger.info('Branches by city retrieved successfully', {
        userId: req.user.id,
        city,
        resultCount: branches.length
      });

      return successResponse(res, branches, `Branches in ${city} retrieved successfully`);

    } catch (error) {
      logger.error('Get branches by city controller error:', error);
      next(error);
    }
  }

  /**
   * Find nearby branches
   * GET /api/branches/nearby
   */
  async findNearbyBranches(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { latitude, longitude, radius = 10, limit = 20 } = req.query;

      const branches = await BranchesService.findNearbyBranches(
        parseFloat(latitude),
        parseFloat(longitude),
        parseFloat(radius),
        { limit: parseInt(limit) },
        req.user
      );

      logger.info('Nearby branches found', {
        userId: req.user.id,
        latitude,
        longitude,
        radius,
        resultCount: branches.length
      });

      return successResponse(res, branches, 'Nearby branches retrieved successfully');

    } catch (error) {
      logger.error('Find nearby branches controller error:', error);
      next(error);
    }
  }

  /**
   * Get branch statistics
   * GET /api/branches/stats
   */
  async getBranchStats(req, res, next) {
    try {
      const stats = await BranchesService.getBranchStats(req.user);

      logger.info('Branch stats retrieved successfully', {
        userId: req.user.id
      });

      return successResponse(res, stats, 'Branch statistics retrieved successfully');

    } catch (error) {
      logger.error('Get branch stats controller error:', error);
      next(error);
    }
  }

  /**
   * Get active branches (simple list)
   * GET /api/branches/active
   */
  async getActiveBranches(req, res, next) {
    try {
      const { limit = 100 } = req.query;
      const branches = await BranchesService.getActiveBranches({
        limit: parseInt(limit)
      }, req.user);

      logger.info('Active branches retrieved successfully', {
        userId: req.user.id,
        resultCount: branches.length
      });

      return successResponse(res, branches, 'Active branches retrieved successfully');

    } catch (error) {
      logger.error('Get active branches controller error:', error);
      next(error);
    }
  }

  /**
   * Update branch images
   * POST /api/branches/:id/images
   */
  async updateBranchImages(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { images } = req.body;

      const branch = await BranchesService.updateBranchImages(parseInt(id), images, req.user);

      logger.info('Branch images updated successfully', {
        userId: req.user.id,
        userRole: req.user.role,
        branchId: id,
        imageCount: images.length
      });

      return successResponse(res, branch, 'Branch images updated successfully');

    } catch (error) {
      logger.error('Update branch images controller error:', error);
      next(error);
    }
  }

  /**
   * Check branch open status
   * GET /api/branches/:id/status
   */
  async checkBranchOpenStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { datetime } = req.query;

      const checkTime = datetime ? new Date(datetime) : new Date();
      const status = await BranchesService.checkBranchOpenStatus(parseInt(id), checkTime, req.user);

      logger.info('Branch open status checked', {
        userId: req.user.id,
        branchId: id,
        isOpen: status.isOpen
      });

      return successResponse(res, status, 'Branch status retrieved successfully');

    } catch (error) {
      logger.error('Check branch open status controller error:', error);
      next(error);
    }
  }
}

module.exports = new BranchesController();
