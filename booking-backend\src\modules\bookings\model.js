/**
 * Booking Model
 * Booking management
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

const Booking = sequelize.define('Booking', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'Unique booking identifier'
  },
  bookingCode: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    field: 'booking_code',
    comment: 'Unique booking code for customer reference'
  },
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'customer_id',
    references: {
      model: 'customers',
      key: 'id'
    },
    comment: 'Customer who made the booking'
  },
  serviceId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'service_id',
    references: {
      model: 'services',
      key: 'id'
    },
    comment: 'Service being booked'
  },
  branchId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'branch_id',
    references: {
      model: 'branches',
      key: 'id'
    },
    comment: 'Branch where service will be provided'
  },
  // formId: {
  //   type: DataTypes.INTEGER,
  //   allowNull: true,
  //   field: 'form_id',
  //   references: {
  //     model: 'forms',
  //     key: 'id'
  //   },
  //   comment: 'Form used to create this booking (if any)'
  // },
  employeeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'employee_id',
    references: {
      model: 'employees',
      key: 'id'
    },
    comment: 'Assigned employee (optional, can be auto-assigned)'
  },
  bookingDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    field: 'booking_date',
    comment: 'Date of the booking'
  },
  startTime: {
    type: DataTypes.TIME,
    allowNull: false,
    field: 'start_time',
    comment: 'Start time of the booking'
  },
  endTime: {
    type: DataTypes.TIME,
    allowNull: false,
    field: 'end_time',
    comment: 'End time of the booking'
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Duration in minutes'
  },
  status: {
    type: DataTypes.ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'),
    defaultValue: 'pending',
    comment: 'Current booking status'
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    field: 'total_amount',
    comment: 'Total amount for the booking'
  },
  discountAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'discount_amount',
    comment: 'Discount applied to the booking'
  },
  finalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    field: 'final_amount',
    comment: 'Final amount after discount'
  },
  paymentStatus: {
    type: DataTypes.ENUM('pending', 'partial', 'paid', 'refunded'),
    defaultValue: 'pending',
    field: 'payment_status',
    comment: 'Payment status of the booking'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional notes for the booking'
  },
  customerNotes: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'customer_notes',
    comment: 'Notes from customer (special requests, preferences)'
  },
  internalNotes: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'internal_notes',
    comment: 'Internal notes for staff'
  },
  cancellationReason: {
    type: DataTypes.STRING(500),
    allowNull: true,
    field: 'cancellation_reason',
    comment: 'Reason for cancellation'
  },
  cancelledBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'cancelled_by',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who cancelled the booking'
  },
  cancelledAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'cancelled_at',
    comment: 'When the booking was cancelled'
  },
  confirmedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'confirmed_by',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Staff who confirmed the booking'
  },
  confirmedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'confirmed_at',
    comment: 'When the booking was confirmed'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'completed_at',
    comment: 'When the service was completed'
  },
  // rating: {
  //   type: DataTypes.INTEGER,
  //   allowNull: true,
  //   validate: {
  //     min: 1,
  //     max: 5
  //   },
  //   comment: 'Customer rating (1-5 stars)'
  // },
  // review: {
  //   type: DataTypes.TEXT,
  //   allowNull: true,
  //   comment: 'Customer review'
  // }
}, {
  tableName: 'bookings',
  timestamps: true,
  underscored: true,
  paranoid: true,
  indexes: [
    {
      fields: ['customer_id']
    },
    {
      fields: ['service_id']
    },
    {
      fields: ['branch_id']
    },
    {
      fields: ['employee_id']
    },
    {
      fields: ['booking_date']
    },
    {
      fields: ['status']
    },
    {
      fields: ['booking_code'],
      unique: true
    },
    {
      fields: ['booking_date', 'start_time', 'end_time']
    }
  ]
});

// Define associations
Booking.associate = (models) => {
  // Booking belongs to Customer
  Booking.belongsTo(models.customers, {
    foreignKey: 'customer_id',
    as: 'customer'
  });

  // Booking belongs to Service
  Booking.belongsTo(models.services, {
    foreignKey: 'service_id',
    as: 'service'
  });

  // Booking belongs to Branch
  Booking.belongsTo(models.branches, {
    foreignKey: 'branch_id',
    as: 'branch'
  });

  // Booking belongs to Employee (optional)
  Booking.belongsTo(models.employees, {
    foreignKey: 'employee_id',
    as: 'employee',
    allowNull: true
  });

  // Booking belongs to User (who cancelled)
  Booking.belongsTo(models.users, {
    foreignKey: 'cancelled_by',
    as: 'cancelledByUser'
  });

  // Booking belongs to User (who confirmed)
  Booking.belongsTo(models.users, {
    foreignKey: 'confirmed_by',
    as: 'confirmedByUser'
  });

  // Booking has many Payments
  Booking.hasMany(models.payments, {
    foreignKey: 'booking_id',
    as: 'payments'
  });

  // Booking has many Notifications
  Booking.hasMany(models.notifications, {
    foreignKey: 'booking_id',
    as: 'notifications'
  });

  // Booking belongs to Form (optional) - TODO: Uncomment after adding form_id column to database
  // Booking.belongsTo(models.forms, {
  //   foreignKey: 'form_id',
  //   as: 'form',
  //   allowNull: true
  // });
};

// Instance methods
Booking.prototype.generateBookingCode = function() {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `BK${date}${random}`;
};

Booking.prototype.canBeCancelled = function() {
  const now = new Date();
  const bookingDateTime = new Date(`${this.bookingDate} ${this.startTime}`);
  const hoursDiff = (bookingDateTime - now) / (1000 * 60 * 60);

  return ['pending', 'confirmed'].includes(this.status) && hoursDiff >= 24;
};

Booking.prototype.canBeModified = function() {
  const now = new Date();
  const bookingDateTime = new Date(`${this.bookingDate} ${this.startTime}`);
  const hoursDiff = (bookingDateTime - now) / (1000 * 60 * 60);

  return ['pending', 'confirmed'].includes(this.status) && hoursDiff >= 2;
};

// Static methods
Booking.generateBookingCode = function() {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `BK${date}${random}`;
};

module.exports = Booking;
