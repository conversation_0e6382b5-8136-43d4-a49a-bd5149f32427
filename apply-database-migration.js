/**
 * <PERSON><PERSON><PERSON> to apply the database migration for form_id column
 */

const mysql = require('mysql2/promise');

async function applyMigration() {
  console.log('🗄️ Applying database migration for form_id column...\n');

  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'password123',
      database: 'spa_booking_development'
    });

    console.log('✅ Connected to database');

    // Check if form_id column already exists
    console.log('🔍 Checking if form_id column exists...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'spa_booking_development' 
      AND TABLE_NAME = 'bookings' 
      AND COLUMN_NAME = 'form_id'
    `);

    if (columns.length > 0) {
      console.log('✅ form_id column already exists');
      return;
    }

    console.log('➕ Adding form_id column to bookings table...');

    // Add form_id column
    await connection.execute(`
      ALTER TABLE bookings 
      ADD COLUMN form_id INT NULL 
      AFTER branch_id
    `);

    console.log('✅ form_id column added');

    // Add foreign key constraint
    console.log('🔗 Adding foreign key constraint...');
    await connection.execute(`
      ALTER TABLE bookings 
      ADD CONSTRAINT fk_bookings_form_id 
      FOREIGN KEY (form_id) REFERENCES forms(id) 
      ON UPDATE CASCADE 
      ON DELETE SET NULL
    `);

    console.log('✅ Foreign key constraint added');

    // Add index for performance
    console.log('📊 Adding index for form_id...');
    await connection.execute(`
      CREATE INDEX idx_bookings_form_id ON bookings(form_id)
    `);

    console.log('✅ Index added');

    // Verify the changes
    console.log('🔍 Verifying migration...');
    const [newColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'spa_booking_development' 
      AND TABLE_NAME = 'bookings' 
      AND COLUMN_NAME = 'form_id'
    `);

    if (newColumns.length > 0) {
      console.log('✅ Migration successful!');
      console.log('Column details:', newColumns[0]);
    } else {
      console.log('❌ Migration verification failed');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    
    if (error.code === 'ER_DUP_KEYNAME') {
      console.log('ℹ️ Index already exists, continuing...');
    } else if (error.code === 'ER_DUP_FIELDNAME') {
      console.log('ℹ️ Column already exists, continuing...');
    } else {
      throw error;
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Test the booking creation after migration
async function testBookingAfterMigration() {
  console.log('\n🧪 Testing booking creation after migration...');
  
  const axios = require('axios');
  
  try {
    const bookingData = {
      formSlug: 'cng-ty-tnhh-nng-vng',
      customerName: 'Migration Test Customer',
      phoneNumber: '0987654321',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-25',
      preferredTime: '14:00',
      specialRequests: 'Test after migration'
    };

    const response = await axios.post('http://localhost:3000/api/public/bookings', bookingData, {
      headers: { 'Content-Type': 'application/json' }
    });

    console.log('✅ Booking creation successful after migration!');
    console.log('Booking ID:', response.data.data.id);
    console.log('Booking Code:', response.data.data.bookingCode);

  } catch (error) {
    console.log('❌ Booking creation still failing:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data?.error?.message || error.message);
  }
}

// Run migration and test
async function runMigrationAndTest() {
  try {
    await applyMigration();
    await testBookingAfterMigration();
  } catch (error) {
    console.error('❌ Process failed:', error.message);
  }
}

if (require.main === module) {
  runMigrationAndTest().catch(console.error);
}

module.exports = { applyMigration, testBookingAfterMigration };
