/**
 * Test forms listing fix with mock server
 */

const express = require('express');
const cors = require('cors');

// Create mock server
const app = express();
app.use(cors());
app.use(express.json());

// Mock authentication middleware
const mockAuth = (req, res, next) => {
  const authHeader = req.header('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: { code: 'UNAUTHORIZED', message: 'Access token required' }
    });
  }
  
  // Mock user
  req.user = {
    id: 1,
    email: '<EMAIL>',
    role: 'admin',
    name: 'Test User'
  };
  next();
};

// Mock forms data
const mockForms = [
  {
    id: 1,
    name: 'Hair Cut Booking Form',
    slug: 'hair-cut-booking',
    status: 'active',
    user_id: 1,
    service: {
      id: 1,
      name: 'Hair Cut',
      price: 50000,
      duration: 60
    },
    branch: {
      id: 1,
      name: '<PERSON> <PERSON>',
      address: '123 Main St',
      phone: '0123456789'
    },
    created_at: '2025-01-01T00:00:00Z',
    publicUrl: 'http://localhost:3001/book/hair-cut-booking',
    embedCode: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800"></iframe>',
    qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=http://localhost:3001/book/hair-cut-booking',
    socialUrls: {
      facebook: 'https://www.facebook.com/sharer/sharer.php?u=http://localhost:3001/book/hair-cut-booking',
      twitter: 'https://twitter.com/intent/tweet?url=http://localhost:3001/book/hair-cut-booking'
    },
    embedCodes: {
      standard: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800"></iframe>',
      compact: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="400" height="600"></iframe>',
      fullHeight: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800"></iframe>',
      mobile: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="100%" height="600"></iframe>',
      styled: '<iframe src="http://localhost:3001/book/hair-cut-booking" width="600" height="800" style="border:none;box-shadow:0 4px 6px rgba(0,0,0,0.1);border-radius:8px;"></iframe>',
      javascript: '<script>/* Dynamic loading code */</script>',
      button: '<button onclick="window.open(\'http://localhost:3001/book/hair-cut-booking\')">Book Now</button>'
    },
    bookingsThisMonth: 5,
    lastBooking: '2025-01-15T10:30:00Z'
  },
  {
    id: 2,
    name: 'Massage Booking Form',
    slug: 'massage-booking',
    status: 'active',
    user_id: 1,
    service: {
      id: 2,
      name: 'Relaxing Massage',
      price: 80000,
      duration: 90
    },
    branch: {
      id: 1,
      name: 'Main Branch',
      address: '123 Main St',
      phone: '0123456789'
    },
    created_at: '2025-01-02T00:00:00Z',
    publicUrl: 'http://localhost:3001/book/massage-booking',
    embedCode: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800"></iframe>',
    qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=http://localhost:3001/book/massage-booking',
    socialUrls: {
      facebook: 'https://www.facebook.com/sharer/sharer.php?u=http://localhost:3001/book/massage-booking',
      twitter: 'https://twitter.com/intent/tweet?url=http://localhost:3001/book/massage-booking'
    },
    embedCodes: {
      standard: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800"></iframe>',
      compact: '<iframe src="http://localhost:3001/book/massage-booking" width="400" height="600"></iframe>',
      fullHeight: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800"></iframe>',
      mobile: '<iframe src="http://localhost:3001/book/massage-booking" width="100%" height="600"></iframe>',
      styled: '<iframe src="http://localhost:3001/book/massage-booking" width="600" height="800" style="border:none;box-shadow:0 4px 6px rgba(0,0,0,0.1);border-radius:8px;"></iframe>',
      javascript: '<script>/* Dynamic loading code */</script>',
      button: '<button onclick="window.open(\'http://localhost:3001/book/massage-booking\')">Book Now</button>'
    },
    bookingsThisMonth: 3,
    lastBooking: '2025-01-14T14:00:00Z'
  }
];

// Mock login endpoint
app.post('/api/auth/login', (req, res) => {
  res.json({
    success: true,
    data: {
      token: 'mock-jwt-token-12345',
      user: {
        id: 1,
        email: '<EMAIL>',
        role: 'admin',
        name: 'Test User'
      }
    },
    message: 'Login successful'
  });
});

// Mock forms listing endpoint
app.get('/api/forms', mockAuth, (req, res) => {
  console.log('📋 Mock forms listing request');
  console.log('   User ID:', req.user.id);
  console.log('   Query params:', req.query);
  
  // Filter forms by user (in real app, this would be done in database)
  const userForms = mockForms.filter(form => form.user_id === req.user.id);
  
  res.json({
    success: true,
    data: {
      forms: userForms,
      pagination: {
        page: 1,
        limit: 10,
        total: userForms.length,
        pages: 1
      }
    },
    message: 'Forms retrieved successfully'
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Mock server is running',
    timestamp: new Date().toISOString()
  });
});

// Start mock server
const PORT = 3000;
const server = app.listen(PORT, () => {
  console.log(`🚀 Mock server running on http://localhost:${PORT}`);
  console.log('📋 Available endpoints:');
  console.log('   POST /api/auth/login');
  console.log('   GET  /api/forms (requires auth)');
  console.log('   GET  /health');
  
  // Run test after server starts
  setTimeout(runTest, 1000);
});

// Test function
async function runTest() {
  const axios = require('axios');
  
  console.log('\n🧪 Testing Forms Listing Fix...\n');
  
  try {
    // Step 1: Login
    console.log('1. Testing login...');
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful, token:', token.substring(0, 20) + '...');
    
    // Step 2: Test forms listing
    console.log('\n2. Testing forms listing...');
    const formsResponse = await axios.get('http://localhost:3000/api/forms', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Forms API successful');
    console.log('   Response structure:', Object.keys(formsResponse.data));
    console.log('   Data structure:', Object.keys(formsResponse.data.data));
    console.log('   Forms count:', formsResponse.data.data.forms.length);
    
    // Step 3: Simulate frontend processing
    console.log('\n3. Simulating frontend processing...');
    const result = { success: true, data: formsResponse.data.data };
    console.log('   Frontend would receive:', {
      success: result.success,
      formsCount: result.data.forms?.length || 0,
      hasFormsArray: Array.isArray(result.data.forms),
      firstFormName: result.data.forms?.[0]?.name
    });
    
    if (result.data.forms && result.data.forms.length > 0) {
      console.log('✅ Forms listing fix is working!');
      console.log('   Forms will be displayed in the frontend');
      
      result.data.forms.forEach((form, index) => {
        console.log(`   - Form ${index + 1}: ${form.name} (${form.status})`);
      });
    } else {
      console.log('❌ No forms found or structure issue');
    }
    
    console.log('\n🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  } finally {
    // Close server
    setTimeout(() => {
      console.log('\n🛑 Shutting down mock server...');
      server.close();
    }, 2000);
  }
}

module.exports = { app };
