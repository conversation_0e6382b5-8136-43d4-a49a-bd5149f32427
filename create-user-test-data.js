/**
 * <PERSON><PERSON><PERSON> to create test data for user to enable form creation
 * Run this to fix the "no services" issue in frontend
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function createUserTestData() {
  console.log('🔧 Creating test data for form creation...\n');

  try {
    // 1. Login with existing user
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    console.log(`✅ Login successful`);
    console.log(`   User ID: ${user.id}`);
    console.log(`   User Email: ${user.email}\n`);

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Check existing data
    console.log('2. Checking existing data...');
    const [branchesRes, servicesRes] = await Promise.all([
      axios.get(`${API_BASE}/branches`, { headers }).catch(e => ({ data: { data: { data: [] } } })),
      axios.get(`${API_BASE}/services`, { headers }).catch(e => ({ data: { data: { data: [] } } }))
    ]);

    const existingBranches = branchesRes.data.data.data || [];
    const existingServices = servicesRes.data.data.data || [];
    
    console.log(`   Existing branches: ${existingBranches.length}`);
    console.log(`   Existing services: ${existingServices.length}\n`);

    let branchId = null;

    // 3. Create branch if needed
    if (existingBranches.length === 0) {
      console.log('3. Creating branch...');
      const timestamp = Date.now();
      const branchData = {
        name: `My Beauty Salon`,
        address: '123 Beauty Street, District 1',
        phone: `0999${timestamp.toString().slice(-6)}`,
        city: 'Ho Chi Minh City',
        district: 'District 1',
        ward: 'Ward 1',
        description: 'Main salon location for beauty services'
      };

      try {
        const branchResponse = await axios.post(`${API_BASE}/branches`, branchData, { headers });
        branchId = branchResponse.data.data.id;
        console.log(`✅ Branch created: ${branchResponse.data.data.name} (ID: ${branchId})\n`);
      } catch (error) {
        if (error.response?.data?.error?.code === 'PHONE_EXISTS') {
          // Try with different phone
          branchData.phone = `0888${timestamp.toString().slice(-6)}`;
          const branchResponse = await axios.post(`${API_BASE}/branches`, branchData, { headers });
          branchId = branchResponse.data.data.id;
          console.log(`✅ Branch created: ${branchResponse.data.data.name} (ID: ${branchId})\n`);
        } else {
          throw error;
        }
      }
    } else {
      branchId = existingBranches[0].id;
      console.log(`3. Using existing branch: ${existingBranches[0].name} (ID: ${branchId})\n`);
    }

    // 4. Create services if needed
    if (existingServices.length === 0) {
      console.log('4. Creating services...');
      
      const services = [
        {
          name: 'Hair Cut & Style',
          description: 'Professional hair cutting and styling service',
          duration: 60,
          price: 150000,
          category: 'hair_care',
          branchId: branchId
        },
        {
          name: 'Facial Treatment',
          description: 'Deep cleansing facial treatment',
          duration: 90,
          price: 200000,
          category: 'facial',
          branchId: branchId
        },
        {
          name: 'Manicure & Pedicure',
          description: 'Complete nail care service',
          duration: 75,
          price: 120000,
          category: 'nail_care',
          branchId: branchId
        }
      ];

      for (const serviceData of services) {
        try {
          const serviceResponse = await axios.post(`${API_BASE}/services`, serviceData, { headers });
          console.log(`✅ Service created: ${serviceResponse.data.data.name} (ID: ${serviceResponse.data.data.id})`);
        } catch (error) {
          console.log(`❌ Failed to create service "${serviceData.name}": ${error.response?.data?.error?.message || error.message}`);
        }
      }
    } else {
      console.log(`4. Services already exist (${existingServices.length} services)\n`);
    }

    // 5. Verify final state
    console.log('\n5. Verifying final state...');
    const finalBranchesRes = await axios.get(`${API_BASE}/branches`, { headers });
    const finalServicesRes = await axios.get(`${API_BASE}/services`, { headers });
    
    const finalBranches = finalBranchesRes.data.data.data || [];
    const finalServices = finalServicesRes.data.data.data || [];
    
    console.log(`✅ Final state:`);
    console.log(`   Branches: ${finalBranches.length}`);
    console.log(`   Services: ${finalServices.length}`);
    
    if (finalBranches.length > 0 && finalServices.length > 0) {
      console.log('\n🎉 Success! User now has the required data to create forms.');
      console.log('💡 You can now refresh the frontend and the services dropdown should be populated.');
    } else {
      console.log('\n⚠️ Warning: User still missing required data.');
    }

  } catch (error) {
    console.error('❌ Error creating test data:', error.response?.data?.error?.message || error.message);
    if (error.response?.data) {
      console.error('   Full error:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the script
if (require.main === module) {
  createUserTestData().catch(console.error);
}

module.exports = { createUserTestData };
